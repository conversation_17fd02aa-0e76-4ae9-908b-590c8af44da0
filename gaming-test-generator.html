<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gaming Interface Test Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a2e;
            color: white;
        }
        .gaming-interface {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        .gaming-interface::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        .header {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 10px 0;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 14px;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
            position: relative;
            z-index: 1;
        }
        .table-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 14px;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
            position: relative;
            z-index: 1;
        }
        .player-name {
            font-weight: bold;
        }
        .stat-value {
            text-align: center;
            font-weight: bold;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            margin: 10px 5px;
            border-radius: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        canvas {
            border: 2px solid #333;
            margin: 10px 0;
            background: white;
        }
        .controls {
            background: #2a2a3e;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Gaming Interface Test Generator</h1>
    <p>Generate realistic gaming interface images to test the OCR extraction capabilities.</p>
    
    <div class="controls">
        <button onclick="generateCricketTeam()">Generate Cricket Team</button>
        <button onclick="generateFootballTeam()">Generate Football Team</button>
        <button onclick="generateBasketballTeam()">Generate Basketball Team</button>
        <button onclick="generateBaseballTeam()">Generate Baseball Team</button>
    </div>

    <div id="gameInterface"></div>
    <canvas id="canvas" width="1000" height="600" style="display: none;"></canvas>
    
    <div id="downloadSection" style="display: none;">
        <h3>Generated Gaming Interface:</h3>
        <img id="generatedImage" style="max-width: 100%; border: 2px solid #333;">
        <br>
        <button onclick="downloadImage()">Download Image</button>
    </div>

    <script>
        let currentImageData = null;

        function generateCricketTeam() {
            const data = {
                title: "CAPPED ALL-ROUNDERS",
                headers: ["NAME", "BATTING RATING", "BATTING HAND", "BOWLING RATING", "BASE PRICE"],
                rows: [
                    ["A OMARSAI", "55", "RIGHT", "60", "100L"],
                    ["D CRISTEN", "60", "RIGHT", "60", "20L"],
                    ["V SHANKER", "60", "RIGHT", "55", "40L"],
                    ["D WILLAY", "55", "LEFT", "60", "50L"],
                    ["P NAEGI", "55", "LEFT", "65", "75L"],
                    ["T CUREN", "50", "RIGHT", "55", "20L"],
                    ["D WESE", "55", "RIGHT", "60", "20L"]
                ]
            };
            generateGamingInterface(data);
        }

        function generateFootballTeam() {
            const data = {
                title: "PREMIER LEAGUE STRIKERS",
                headers: ["NAME", "PACE", "SHOOTING", "PASSING", "VALUE"],
                rows: [
                    ["M SALAH", "90", "87", "75", "120M"],
                    ["H KANE", "68", "91", "83", "100M"],
                    ["E HAALAND", "89", "94", "65", "150M"],
                    ["K MBAPPE", "97", "89", "78", "180M"],
                    ["R LEWANDOWSKI", "78", "92", "85", "90M"],
                    ["C RONALDO", "81", "93", "82", "85M"],
                    ["L MESSI", "85", "88", "95", "110M"]
                ]
            };
            generateGamingInterface(data);
        }

        function generateBasketballTeam() {
            const data = {
                title: "NBA ALL-STARS",
                headers: ["NAME", "SPEED", "SHOOTING", "DEFENSE", "SALARY"],
                rows: [
                    ["L JAMES", "82", "75", "78", "45M"],
                    ["S CURRY", "86", "99", "65", "48M"],
                    ["K DURANT", "78", "92", "72", "42M"],
                    ["G ANTETOKOUNMPO", "88", "72", "85", "45M"],
                    ["L DONCIC", "75", "87", "68", "35M"],
                    ["J TATUM", "83", "88", "80", "32M"],
                    ["N JOKIC", "65", "82", "88", "47M"]
                ]
            };
            generateGamingInterface(data);
        }

        function generateBaseballTeam() {
            const data = {
                title: "MLB POWER HITTERS",
                headers: ["NAME", "CONTACT", "POWER", "SPEED", "CONTRACT"],
                rows: [
                    ["M TROUT", "88", "92", "85", "35M"],
                    ["A JUDGE", "82", "95", "75", "40M"],
                    ["M BETTS", "90", "85", "88", "32M"],
                    ["F TATIS JR", "85", "88", "92", "28M"],
                    ["V GUERRERO JR", "87", "90", "65", "25M"],
                    ["R ACUNA JR", "83", "87", "95", "30M"],
                    ["J SOTO", "92", "88", "70", "35M"]
                ]
            };
            generateGamingInterface(data);
        }

        function generateGamingInterface(data) {
            const container = document.getElementById('gameInterface');
            
            let html = `<div class="gaming-interface">`;
            html += `<div class="header">${data.title}</div>`;
            
            // Headers
            html += `<div class="table-header">`;
            data.headers.forEach(header => {
                html += `<div>${header}</div>`;
            });
            html += `</div>`;
            
            // Rows
            data.rows.forEach(row => {
                html += `<div class="table-row">`;
                row.forEach((cell, index) => {
                    const className = index === 0 ? 'player-name' : 'stat-value';
                    html += `<div class="${className}">${cell}</div>`;
                });
                html += `</div>`;
            });
            
            html += `</div>`;
            html += `<button onclick="convertToImage('${data.title}')">Convert to Image</button>`;
            
            container.innerHTML = html;
        }

        function convertToImage(title) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const container = document.querySelector('.gaming-interface');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Set text properties
            ctx.fillStyle = 'white';
            ctx.shadowColor = 'rgba(0,0,0,0.7)';
            ctx.shadowBlur = 3;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // Draw title
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, canvas.width / 2, 60);
            
            // Get table data
            const headers = container.querySelector('.table-header');
            const rows = container.querySelectorAll('.table-row');
            
            let y = 120;
            const colWidths = [200, 120, 120, 120, 120];
            const startX = 50;
            
            // Draw headers
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            let x = startX;
            const headerTexts = headers.querySelectorAll('div');
            headerTexts.forEach((header, index) => {
                ctx.fillText(header.textContent, x, y);
                x += colWidths[index];
            });
            
            // Draw underline for headers
            ctx.strokeStyle = 'rgba(255,255,255,0.5)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, y + 10);
            ctx.lineTo(startX + colWidths.reduce((a, b) => a + b, 0) - 50, y + 10);
            ctx.stroke();
            
            y += 40;
            
            // Draw rows
            ctx.font = '16px Arial';
            rows.forEach(row => {
                const cells = row.querySelectorAll('div');
                x = startX;
                cells.forEach((cell, index) => {
                    if (index === 0) {
                        ctx.font = 'bold 16px Arial';
                    } else {
                        ctx.font = '16px Arial';
                        ctx.textAlign = 'center';
                    }
                    
                    const textX = index === 0 ? x : x + colWidths[index] / 2;
                    ctx.fillText(cell.textContent, textX, y);
                    x += colWidths[index];
                });
                y += 35;
                ctx.textAlign = 'left';
            });
            
            // Convert to image
            currentImageData = canvas.toDataURL('image/png');
            const img = document.getElementById('generatedImage');
            img.src = currentImageData;
            
            document.getElementById('downloadSection').style.display = 'block';
        }

        function downloadImage() {
            if (!currentImageData) return;
            
            const link = document.createElement('a');
            link.download = 'gaming-interface-test.png';
            link.href = currentImageData;
            link.click();
        }
    </script>
</body>
</html>
