<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Image Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .table-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            background: white;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Test Image Generator for OCR</h1>
    <p>Generate test images with tabular data to test the Image to Excel converter.</p>
    
    <div>
        <button onclick="generateSalesData()">Generate Sales Data</button>
        <button onclick="generateInventoryData()">Generate Inventory Data</button>
        <button onclick="generateEmployeeData()">Generate Employee Data</button>
        <button onclick="generateFinancialData()">Generate Financial Data</button>
    </div>

    <div id="tableContainer"></div>
    <canvas id="canvas" width="800" height="600" style="display: none;"></canvas>
    
    <div id="downloadSection" style="display: none;">
        <h3>Generated Image:</h3>
        <img id="generatedImage" style="max-width: 100%; border: 1px solid #ccc;">
        <br>
        <button onclick="downloadImage()">Download Image</button>
    </div>

    <script>
        let currentImageData = null;

        function generateSalesData() {
            const data = [
                ['Product', 'Quantity', 'Price', 'Total'],
                ['Laptop', '15', '$899.99', '$13,499.85'],
                ['Mouse', '50', '$29.99', '$1,499.50'],
                ['Keyboard', '30', '$79.99', '$2,399.70'],
                ['Monitor', '20', '$299.99', '$5,999.80'],
                ['Headphones', '25', '$149.99', '$3,749.75']
            ];
            generateTable('Sales Report - Q4 2024', data);
        }

        function generateInventoryData() {
            const data = [
                ['Item Code', 'Description', 'Stock', 'Location'],
                ['INV001', 'Office Chair', '45', 'Warehouse A'],
                ['INV002', 'Desk Lamp', '120', 'Warehouse B'],
                ['INV003', 'Filing Cabinet', '30', 'Warehouse A'],
                ['INV004', 'Whiteboard', '15', 'Warehouse C'],
                ['INV005', 'Printer Paper', '200', 'Warehouse B']
            ];
            generateTable('Inventory Status Report', data);
        }

        function generateEmployeeData() {
            const data = [
                ['Employee ID', 'Name', 'Department', 'Salary'],
                ['EMP001', 'John Smith', 'Engineering', '$75,000'],
                ['EMP002', 'Sarah Johnson', 'Marketing', '$65,000'],
                ['EMP003', 'Mike Brown', 'Sales', '$70,000'],
                ['EMP004', 'Lisa Davis', 'HR', '$60,000'],
                ['EMP005', 'Tom Wilson', 'Finance', '$80,000']
            ];
            generateTable('Employee Directory', data);
        }

        function generateFinancialData() {
            const data = [
                ['Month', 'Revenue', 'Expenses', 'Profit'],
                ['January', '$125,000', '$85,000', '$40,000'],
                ['February', '$135,000', '$90,000', '$45,000'],
                ['March', '$145,000', '$95,000', '$50,000'],
                ['April', '$155,000', '$100,000', '$55,000'],
                ['May', '$165,000', '$105,000', '$60,000']
            ];
            generateTable('Financial Summary 2024', data);
        }

        function generateTable(title, data) {
            const container = document.getElementById('tableContainer');
            
            let html = `<div class="table-container">`;
            html += `<h2>${title}</h2>`;
            html += `<table>`;
            
            data.forEach((row, index) => {
                if (index === 0) {
                    html += `<thead><tr>`;
                    row.forEach(cell => {
                        html += `<th>${cell}</th>`;
                    });
                    html += `</tr></thead><tbody>`;
                } else {
                    html += `<tr>`;
                    row.forEach(cell => {
                        html += `<td>${cell}</td>`;
                    });
                    html += `</tr>`;
                }
            });
            
            html += `</tbody></table>`;
            html += `<button onclick="convertToImage('${title}')">Convert to Image</button>`;
            html += `</div>`;
            
            container.innerHTML = html;
        }

        function convertToImage(title) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const container = document.querySelector('.table-container');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Set font
            ctx.fillStyle = 'black';
            ctx.font = '16px Arial';
            
            // Draw title
            ctx.font = 'bold 20px Arial';
            ctx.fillText(title, 50, 40);
            
            // Get table data
            const table = container.querySelector('table');
            const rows = table.querySelectorAll('tr');
            
            let y = 80;
            const cellWidth = 150;
            const cellHeight = 30;
            
            rows.forEach((row, rowIndex) => {
                const cells = row.querySelectorAll('th, td');
                let x = 50;
                
                cells.forEach((cell, cellIndex) => {
                    // Draw cell border
                    ctx.strokeRect(x, y, cellWidth, cellHeight);
                    
                    // Fill header background
                    if (rowIndex === 0) {
                        ctx.fillStyle = '#f0f0f0';
                        ctx.fillRect(x + 1, y + 1, cellWidth - 2, cellHeight - 2);
                        ctx.fillStyle = 'black';
                        ctx.font = 'bold 14px Arial';
                    } else {
                        ctx.font = '14px Arial';
                    }
                    
                    // Draw text
                    const text = cell.textContent;
                    const textX = x + 5;
                    const textY = y + 20;
                    ctx.fillText(text, textX, textY);
                    
                    x += cellWidth;
                });
                
                y += cellHeight;
            });
            
            // Convert to image
            currentImageData = canvas.toDataURL('image/png');
            const img = document.getElementById('generatedImage');
            img.src = currentImageData;
            
            document.getElementById('downloadSection').style.display = 'block';
        }

        function downloadImage() {
            if (!currentImageData) return;
            
            const link = document.createElement('a');
            link.download = 'test-table-image.png';
            link.href = currentImageData;
            link.click();
        }
    </script>
</body>
</html>
