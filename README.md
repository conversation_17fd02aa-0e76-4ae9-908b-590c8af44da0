# Image to Excel Converter

A web application that extracts tabular data from multiple images and generates a downloadable Excel file. All processing is done locally in the browser without any external services.

## Features

- **Bulk Image Upload**: Upload 60+ images at once via drag-and-drop or file selection
- **Local OCR Processing**: Uses Tesseract.js for optical character recognition
- **Tabular Data Extraction**: Automatically detects and extracts table-like data from images
- **Data Combination**: Combines extracted data from all images into a single dataset
- **Excel Export**: Generates downloadable Excel files using SheetJS
- **Progress Tracking**: Real-time progress indicators and processing logs
- **Data Preview**: Preview extracted data before downloading
- **No External Dependencies**: All processing happens locally in the browser

## How to Use

1. **Open the Application**: Open `index.html` in a modern web browser
2. **Upload Images**: 
   - Drag and drop images onto the upload zone, or
   - Click the upload zone to select files
   - Supports JPG, PNG, GIF formats
3. **Review Selected Images**: Preview thumbnails of selected images
4. **Process Images**: Click "Process Images" to start OCR extraction
5. **Monitor Progress**: Watch the progress bar and processing log
6. **Preview Results**: Click "Preview Data" to see extracted data
7. **Download Excel**: Click "Download Excel File" to get the combined dataset

## Technical Details

### Libraries Used
- **Tesseract.js**: Client-side OCR for text extraction from images
- **SheetJS (xlsx)**: Excel file generation and manipulation
- **Tailwind CSS**: Responsive UI styling

### Data Structure
Each extracted row includes:
- `source_file`: Original image filename
- `row_number`: Sequential row number within the file
- `raw_text`: Original extracted text line
- `column_1`, `column_2`, etc.: Parsed column data

### Browser Requirements
- Modern browser with JavaScript enabled
- Sufficient RAM for processing multiple images (recommended: 4GB+)
- Local file access permissions

## File Structure

```
image-to-excel/
├── index.html          # Main HTML interface
├── app.js             # Core application logic
└── README.md          # This documentation
```

## Limitations

- OCR accuracy depends on image quality and text clarity
- Table detection uses simple heuristics (may need manual review)
- Processing time increases with number and size of images
- Large images may require more memory

## Tips for Better Results

1. **Image Quality**: Use high-resolution, clear images
2. **Table Format**: Ensure tables have clear column separation
3. **Lighting**: Good contrast between text and background
4. **Orientation**: Ensure images are properly oriented
5. **File Size**: Compress very large images if processing is slow

## Troubleshooting

- **Slow Processing**: Reduce image count or size
- **Poor Extraction**: Check image quality and table clarity
- **Browser Crashes**: Close other tabs, restart browser
- **No Data Extracted**: Verify images contain tabular data

## Privacy & Security

- All processing happens locally in your browser
- No data is sent to external servers
- Images and extracted data remain on your device
- No internet connection required after initial page load
