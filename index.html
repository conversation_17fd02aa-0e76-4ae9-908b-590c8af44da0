<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image to Excel Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@5/dist/tesseract.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .drop-zone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            border-color: #4299e1;
            background-color: #ebf8ff;
        }
        .progress-bar {
            transition: width 0.3s ease;
        }
        .image-preview {
            max-width: 200px;
            max-height: 150px;
            object-fit: cover;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center text-gray-800 mb-8">Image to Excel Converter</h1>
        
        <!-- Upload Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4">Upload Images</h2>
            <div id="dropZone" class="drop-zone border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer">
                <div class="text-gray-600">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="text-lg">Drop images here or click to select</p>
                    <p class="text-sm text-gray-500">Supports JPG, PNG, GIF (up to 60+ images)</p>
                </div>
            </div>
            <input type="file" id="fileInput" multiple accept="image/*" class="hidden">
        </div>

        <!-- Selected Images Preview -->
        <div id="imagePreview" class="bg-white rounded-lg shadow-lg p-6 mb-8 hidden">
            <h2 class="text-2xl font-semibold mb-4">Selected Images (<span id="imageCount">0</span>)</h2>
            <div id="imageGrid" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-4"></div>
            <button id="processBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 disabled:cursor-not-allowed">
                Process Images
            </button>
            <button id="clearBtn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded ml-2">
                Clear All
            </button>
        </div>

        <!-- Processing Section -->
        <div id="processingSection" class="bg-white rounded-lg shadow-lg p-6 mb-8 hidden">
            <h2 class="text-2xl font-semibold mb-4">Processing Images</h2>
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Overall Progress</span>
                    <span id="overallProgress">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="overallProgressBar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
            </div>
            <div id="processingLog" class="bg-gray-50 rounded p-4 h-40 overflow-y-auto text-sm"></div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="bg-white rounded-lg shadow-lg p-6 hidden">
            <h2 class="text-2xl font-semibold mb-4">Extraction Results</h2>
            <div class="mb-4">
                <p class="text-gray-600">Successfully processed <span id="processedCount">0</span> images</p>
                <p class="text-gray-600">Total data rows extracted: <span id="totalRows">0</span></p>
            </div>
            <div class="mb-4">
                <button id="downloadBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Download Excel File
                </button>
                <button id="previewBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded ml-2">
                    Preview Data
                </button>
            </div>
            <div id="dataPreview" class="hidden">
                <h3 class="text-lg font-semibold mb-2">Data Preview</h3>
                <div class="overflow-x-auto">
                    <table id="previewTable" class="min-w-full bg-white border border-gray-300">
                        <thead id="previewTableHead" class="bg-gray-50"></thead>
                        <tbody id="previewTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
