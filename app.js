class ImageToExcelConverter {
    constructor() {
        this.selectedFiles = [];
        this.extractedData = [];
        this.isProcessing = false;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const processBtn = document.getElementById('processBtn');
        const clearBtn = document.getElementById('clearBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const previewBtn = document.getElementById('previewBtn');

        // Drop zone events
        dropZone.addEventListener('click', () => fileInput.click());
        dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
        dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        dropZone.addEventListener('drop', this.handleDrop.bind(this));

        // File input change
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Button events
        processBtn.addEventListener('click', this.processImages.bind(this));
        clearBtn.addEventListener('click', this.clearImages.bind(this));
        downloadBtn.addEventListener('click', this.downloadExcel.bind(this));
        previewBtn.addEventListener('click', this.togglePreview.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        this.addFiles(files);
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.addFiles(files);
    }

    addFiles(files) {
        this.selectedFiles = [...this.selectedFiles, ...files];
        this.updateImagePreview();
    }

    updateImagePreview() {
        const imagePreview = document.getElementById('imagePreview');
        const imageGrid = document.getElementById('imageGrid');
        const imageCount = document.getElementById('imageCount');

        if (this.selectedFiles.length === 0) {
            imagePreview.classList.add('hidden');
            return;
        }

        imagePreview.classList.remove('hidden');
        imageCount.textContent = this.selectedFiles.length;
        imageGrid.innerHTML = '';

        this.selectedFiles.forEach((file, index) => {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'relative';

            const img = document.createElement('img');
            img.className = 'image-preview rounded border';
            img.src = URL.createObjectURL(file);
            img.alt = `Image ${index + 1}`;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 text-xs';
            removeBtn.textContent = '×';
            removeBtn.onclick = () => this.removeImage(index);

            const fileName = document.createElement('p');
            fileName.className = 'text-xs text-gray-600 mt-1 truncate';
            fileName.textContent = file.name;

            imageContainer.appendChild(img);
            imageContainer.appendChild(removeBtn);
            imageContainer.appendChild(fileName);
            imageGrid.appendChild(imageContainer);
        });
    }

    removeImage(index) {
        this.selectedFiles.splice(index, 1);
        this.updateImagePreview();
    }

    clearImages() {
        this.selectedFiles = [];
        this.extractedData = [];
        this.updateImagePreview();
        document.getElementById('resultsSection').classList.add('hidden');
        document.getElementById('processingSection').classList.add('hidden');
    }

    async processImages() {
        if (this.selectedFiles.length === 0 || this.isProcessing) return;

        this.isProcessing = true;
        this.extractedData = [];

        const processingSection = document.getElementById('processingSection');
        const resultsSection = document.getElementById('resultsSection');
        const processBtn = document.getElementById('processBtn');

        processingSection.classList.remove('hidden');
        resultsSection.classList.add('hidden');
        processBtn.disabled = true;

        this.log('Starting image processing...');
        this.log(`Total images to process: ${this.selectedFiles.length}`);

        try {
            // Process in batches to manage memory
            const batchSize = this.selectedFiles.length > 20 ? 5 : 10;
            const totalBatches = Math.ceil(this.selectedFiles.length / batchSize);

            this.log(`Processing in ${totalBatches} batches of ${batchSize} images each`);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * batchSize;
                const endIndex = Math.min(startIndex + batchSize, this.selectedFiles.length);
                const batch = this.selectedFiles.slice(startIndex, endIndex);

                this.log(`Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} images)`);

                // Process batch with parallel processing for smaller batches
                if (batch.length <= 3) {
                    await this.processBatchParallel(batch, startIndex);
                } else {
                    await this.processBatchSequential(batch, startIndex);
                }

                // Force garbage collection hint
                if (window.gc) {
                    window.gc();
                }

                // Small delay between batches to prevent browser freezing
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            this.log('Processing completed!');
            this.showResults();
        } catch (error) {
            this.log(`Error: ${error.message}`);
        } finally {
            this.isProcessing = false;
            processBtn.disabled = false;
        }
    }

    async processBatchSequential(batch, startIndex) {
        for (let i = 0; i < batch.length; i++) {
            const file = batch[i];
            const globalIndex = startIndex + i;
            const progress = ((globalIndex + 1) / this.selectedFiles.length) * 100;

            this.updateProgress(progress, `Processing ${file.name}...`);
            this.log(`Processing image ${globalIndex + 1}/${this.selectedFiles.length}: ${file.name}`);

            try {
                const extractedText = await this.extractTextFromImage(file);
                const tableData = this.parseTableData(extractedText, file.name);

                if (tableData.length > 0) {
                    this.extractedData.push(...tableData);
                    this.log(`✓ Extracted ${tableData.length} rows from ${file.name}`);
                } else {
                    this.log(`⚠ No tabular data found in ${file.name}`);
                }
            } catch (error) {
                this.log(`✗ Error processing ${file.name}: ${error.message}`);
            }
        }
    }

    async processBatchParallel(batch, startIndex) {
        const promises = batch.map(async (file, i) => {
            const globalIndex = startIndex + i;
            this.log(`Starting parallel processing: ${file.name}`);

            try {
                const extractedText = await this.extractTextFromImage(file);
                const tableData = this.parseTableData(extractedText, file.name);

                const progress = ((globalIndex + 1) / this.selectedFiles.length) * 100;
                this.updateProgress(progress, `Completed ${file.name}`);

                if (tableData.length > 0) {
                    this.log(`✓ Extracted ${tableData.length} rows from ${file.name}`);
                    return tableData;
                } else {
                    this.log(`⚠ No tabular data found in ${file.name}`);
                    return [];
                }
            } catch (error) {
                this.log(`✗ Error processing ${file.name}: ${error.message}`);
                return [];
            }
        });

        const results = await Promise.all(promises);
        results.forEach(tableData => {
            if (tableData.length > 0) {
                this.extractedData.push(...tableData);
            }
        });
    }

    async extractTextFromImage(file) {
        return new Promise((resolve, reject) => {
            // Optimize image before OCR
            this.optimizeImageForOCR(file).then(optimizedFile => {
                const worker = Tesseract.createWorker();

                worker.load().then(() => {
                    return worker.loadLanguage('eng');
                }).then(() => {
                    return worker.initialize('eng', {
                        // Optimize for table detection
                        tessedit_pageseg_mode: Tesseract.PSM.AUTO,
                        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:\'\"<>?/~` '
                    });
                }).then(() => {
                    return worker.recognize(optimizedFile);
                }).then(({ data: { text } }) => {
                    worker.terminate();
                    resolve(text);
                }).catch(error => {
                    worker.terminate();
                    reject(error);
                });
            }).catch(error => {
                reject(error);
            });
        });
    }

    async optimizeImageForOCR(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate optimal size (max 1920px width, maintain aspect ratio)
                const maxWidth = 1920;
                const maxHeight = 1080;
                let { width, height } = img;

                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }

                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and enhance image
                ctx.drawImage(img, 0, 0, width, height);

                // Apply contrast enhancement
                const imageData = ctx.getImageData(0, 0, width, height);
                this.enhanceContrast(imageData);
                ctx.putImageData(imageData, 0, 0);

                // Convert back to blob
                canvas.toBlob(resolve, 'image/png', 0.9);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    enhanceContrast(imageData) {
        const data = imageData.data;
        const factor = 1.2; // Contrast factor
        const intercept = 128 * (1 - factor);

        for (let i = 0; i < data.length; i += 4) {
            // Apply contrast to RGB channels
            data[i] = Math.max(0, Math.min(255, data[i] * factor + intercept));     // Red
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor + intercept)); // Green
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor + intercept)); // Blue
            // Alpha channel remains unchanged
        }
    }

    parseTableData(text, fileName) {
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        const tableData = [];

        // Enhanced table detection with multiple strategies
        const detectedTables = this.detectTables(lines);

        detectedTables.forEach((table, tableIndex) => {
            table.rows.forEach((row, rowIndex) => {
                const rowData = {
                    source_file: fileName,
                    table_number: tableIndex + 1,
                    row_number: rowIndex + 1,
                    raw_text: row.original
                };

                // Add cells as columns
                row.cells.forEach((cell, cellIndex) => {
                    rowData[`column_${cellIndex + 1}`] = cell;
                });

                tableData.push(rowData);
            });
        });

        return tableData;
    }

    detectTables(lines) {
        const tables = [];
        let currentTable = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip very short lines
            if (line.length < 5) {
                if (currentTable && currentTable.rows.length > 0) {
                    tables.push(currentTable);
                    currentTable = null;
                }
                continue;
            }

            const parsedRow = this.parseTableRow(line);

            if (parsedRow && parsedRow.cells.length >= 2) {
                if (!currentTable) {
                    currentTable = { rows: [] };
                }
                currentTable.rows.push(parsedRow);
            } else {
                // End current table if we hit a non-table line
                if (currentTable && currentTable.rows.length > 0) {
                    tables.push(currentTable);
                    currentTable = null;
                }
            }
        }

        // Add the last table if it exists
        if (currentTable && currentTable.rows.length > 0) {
            tables.push(currentTable);
        }

        return tables;
    }

    parseTableRow(line) {
        const original = line;
        let cells = [];

        // Strategy 1: Split by multiple spaces (most common)
        const spaceSplit = line.split(/\s{2,}/).map(cell => cell.trim()).filter(cell => cell.length > 0);
        if (spaceSplit.length >= 2) {
            cells = spaceSplit;
        }

        // Strategy 2: Split by tabs
        if (cells.length < 2) {
            const tabSplit = line.split('\t').map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (tabSplit.length >= 2) {
                cells = tabSplit;
            }
        }

        // Strategy 3: Split by pipes or vertical bars
        if (cells.length < 2) {
            const pipeSplit = line.split(/[|│]/).map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (pipeSplit.length >= 2) {
                cells = pipeSplit;
            }
        }

        // Strategy 4: Split by commas (CSV-like)
        if (cells.length < 2) {
            const commaSplit = line.split(',').map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (commaSplit.length >= 2) {
                cells = commaSplit;
            }
        }

        // Strategy 5: Detect aligned columns by position
        if (cells.length < 2) {
            cells = this.detectAlignedColumns(line);
        }

        return cells.length >= 2 ? { original, cells } : null;
    }

    detectAlignedColumns(line) {
        // Simple column detection based on character positions
        const words = [];
        let currentWord = '';
        let wordStart = 0;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === ' ') {
                if (currentWord.trim().length > 0) {
                    words.push({
                        text: currentWord.trim(),
                        start: wordStart,
                        end: i
                    });
                    currentWord = '';
                }
            } else {
                if (currentWord === '') {
                    wordStart = i;
                }
                currentWord += char;
            }
        }

        // Add the last word
        if (currentWord.trim().length > 0) {
            words.push({
                text: currentWord.trim(),
                start: wordStart,
                end: line.length
            });
        }

        // Group words into columns based on spacing
        const columns = [];
        let currentColumn = [];

        for (let i = 0; i < words.length; i++) {
            const word = words[i];
            const nextWord = words[i + 1];

            currentColumn.push(word.text);

            // If there's a significant gap to the next word, end the column
            if (nextWord && (nextWord.start - word.end) > 3) {
                columns.push(currentColumn.join(' '));
                currentColumn = [];
            }
        }

        // Add the last column
        if (currentColumn.length > 0) {
            columns.push(currentColumn.join(' '));
        }

        return columns;
    }

    updateProgress(percentage, message) {
        const progressBar = document.getElementById('overallProgressBar');
        const progressText = document.getElementById('overallProgress');
        
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = `${Math.round(percentage)}%`;
        
        if (message) {
            this.log(message);
        }
    }

    log(message) {
        const logElement = document.getElementById('processingLog');
        const timestamp = new Date().toLocaleTimeString();
        logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logElement.scrollTop = logElement.scrollHeight;
    }

    showResults() {
        const resultsSection = document.getElementById('resultsSection');
        const processedCount = document.getElementById('processedCount');
        const totalRows = document.getElementById('totalRows');
        
        resultsSection.classList.remove('hidden');
        processedCount.textContent = this.selectedFiles.length;
        totalRows.textContent = this.extractedData.length;
    }

    togglePreview() {
        const dataPreview = document.getElementById('dataPreview');
        const previewBtn = document.getElementById('previewBtn');
        
        if (dataPreview.classList.contains('hidden')) {
            this.generatePreview();
            dataPreview.classList.remove('hidden');
            previewBtn.textContent = 'Hide Preview';
        } else {
            dataPreview.classList.add('hidden');
            previewBtn.textContent = 'Preview Data';
        }
    }

    generatePreview() {
        if (this.extractedData.length === 0) return;
        
        const tableHead = document.getElementById('previewTableHead');
        const tableBody = document.getElementById('previewTableBody');
        
        // Get all unique column names
        const allColumns = new Set();
        this.extractedData.forEach(row => {
            Object.keys(row).forEach(key => allColumns.add(key));
        });
        
        const columns = Array.from(allColumns);
        
        // Create header
        tableHead.innerHTML = `<tr>${columns.map(col => `<th class="px-4 py-2 border">${col}</th>`).join('')}</tr>`;
        
        // Create body (show first 10 rows)
        const previewData = this.extractedData.slice(0, 10);
        tableBody.innerHTML = previewData.map(row => 
            `<tr>${columns.map(col => `<td class="px-4 py-2 border">${row[col] || ''}</td>`).join('')}</tr>`
        ).join('');
    }

    downloadExcel() {
        if (this.extractedData.length === 0) {
            alert('No data to download');
            return;
        }

        // Create workbook
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(this.extractedData);
        
        XLSX.utils.book_append_sheet(wb, ws, 'Extracted Data');
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
        const filename = `extracted_data_${timestamp}.xlsx`;
        
        // Download file
        XLSX.writeFile(wb, filename);
        
        this.log(`Excel file downloaded: ${filename}`);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new ImageToExcelConverter();
});
