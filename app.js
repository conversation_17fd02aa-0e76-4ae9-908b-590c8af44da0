class ImageToExcelConverter {
    constructor() {
        this.selectedFiles = [];
        this.extractedData = [];
        this.isProcessing = false;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const processBtn = document.getElementById('processBtn');
        const clearBtn = document.getElementById('clearBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const previewBtn = document.getElementById('previewBtn');

        // Drop zone events
        dropZone.addEventListener('click', () => fileInput.click());
        dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
        dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        dropZone.addEventListener('drop', this.handleDrop.bind(this));

        // File input change
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Button events
        processBtn.addEventListener('click', this.processImages.bind(this));
        clearBtn.addEventListener('click', this.clearImages.bind(this));
        downloadBtn.addEventListener('click', this.downloadExcel.bind(this));
        previewBtn.addEventListener('click', this.togglePreview.bind(this));
    }

    handleDragOver(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        document.getElementById('dropZone').classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        this.addFiles(files);
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.addFiles(files);
    }

    addFiles(files) {
        this.selectedFiles = [...this.selectedFiles, ...files];
        this.updateImagePreview();
    }

    updateImagePreview() {
        const imagePreview = document.getElementById('imagePreview');
        const imageGrid = document.getElementById('imageGrid');
        const imageCount = document.getElementById('imageCount');

        if (this.selectedFiles.length === 0) {
            imagePreview.classList.add('hidden');
            return;
        }

        imagePreview.classList.remove('hidden');
        imageCount.textContent = this.selectedFiles.length;
        imageGrid.innerHTML = '';

        this.selectedFiles.forEach((file, index) => {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'relative';

            const img = document.createElement('img');
            img.className = 'image-preview rounded border';
            img.src = URL.createObjectURL(file);
            img.alt = `Image ${index + 1}`;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 text-xs';
            removeBtn.textContent = '×';
            removeBtn.onclick = () => this.removeImage(index);

            const fileName = document.createElement('p');
            fileName.className = 'text-xs text-gray-600 mt-1 truncate';
            fileName.textContent = file.name;

            imageContainer.appendChild(img);
            imageContainer.appendChild(removeBtn);
            imageContainer.appendChild(fileName);
            imageGrid.appendChild(imageContainer);
        });
    }

    removeImage(index) {
        this.selectedFiles.splice(index, 1);
        this.updateImagePreview();
    }

    clearImages() {
        this.selectedFiles = [];
        this.extractedData = [];
        this.updateImagePreview();
        document.getElementById('resultsSection').classList.add('hidden');
        document.getElementById('processingSection').classList.add('hidden');
    }

    async processImages() {
        if (this.selectedFiles.length === 0 || this.isProcessing) return;

        this.isProcessing = true;
        this.extractedData = [];

        const processingSection = document.getElementById('processingSection');
        const resultsSection = document.getElementById('resultsSection');
        const processBtn = document.getElementById('processBtn');

        processingSection.classList.remove('hidden');
        resultsSection.classList.add('hidden');
        processBtn.disabled = true;

        this.log('Starting image processing...');
        this.log(`Total images to process: ${this.selectedFiles.length}`);

        try {
            // Process in batches to manage memory
            const batchSize = this.selectedFiles.length > 20 ? 5 : 10;
            const totalBatches = Math.ceil(this.selectedFiles.length / batchSize);

            this.log(`Processing in ${totalBatches} batches of ${batchSize} images each`);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * batchSize;
                const endIndex = Math.min(startIndex + batchSize, this.selectedFiles.length);
                const batch = this.selectedFiles.slice(startIndex, endIndex);

                this.log(`Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} images)`);

                // Process batch with parallel processing for smaller batches
                if (batch.length <= 3) {
                    await this.processBatchParallel(batch, startIndex);
                } else {
                    await this.processBatchSequential(batch, startIndex);
                }

                // Force garbage collection hint
                if (window.gc) {
                    window.gc();
                }

                // Small delay between batches to prevent browser freezing
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            this.log('Processing completed!');
            this.showResults();
        } catch (error) {
            this.log(`Error: ${error.message}`);
        } finally {
            this.isProcessing = false;
            processBtn.disabled = false;
        }
    }

    async processBatchSequential(batch, startIndex) {
        for (let i = 0; i < batch.length; i++) {
            const file = batch[i];
            const globalIndex = startIndex + i;
            const progress = ((globalIndex + 1) / this.selectedFiles.length) * 100;

            this.updateProgress(progress, `Processing ${file.name}...`);
            this.log(`Processing image ${globalIndex + 1}/${this.selectedFiles.length}: ${file.name}`);

            try {
                const extractedText = await this.extractTextFromImage(file);
                this.log(`Raw OCR text from ${file.name}:`);
                this.log(`"${extractedText.substring(0, 200)}..."`);

                const tableData = this.parseTableData(extractedText, file.name);

                if (tableData.length > 0) {
                    this.extractedData.push(...tableData);
                    this.log(`✓ Extracted ${tableData.length} rows from ${file.name}`);
                    // Log first few rows for debugging
                    tableData.slice(0, 2).forEach((row, idx) => {
                        this.log(`Row ${idx + 1}: ${JSON.stringify(row)}`);
                    });
                } else {
                    this.log(`⚠ No tabular data found in ${file.name}`);
                    this.log(`Trying alternative parsing methods...`);

                    // Try alternative parsing for gaming interfaces
                    const alternativeData = this.parseGamingInterface(extractedText, file.name);
                    if (alternativeData.length > 0) {
                        this.extractedData.push(...alternativeData);
                        this.log(`✓ Extracted ${alternativeData.length} rows using gaming interface parser`);
                    }
                }
            } catch (error) {
                this.log(`✗ Error processing ${file.name}: ${error.message}`);
            }
        }
    }

    async processBatchParallel(batch, startIndex) {
        const promises = batch.map(async (file, i) => {
            const globalIndex = startIndex + i;
            this.log(`Starting parallel processing: ${file.name}`);

            try {
                const extractedText = await this.extractTextFromImage(file);
                let tableData = this.parseTableData(extractedText, file.name);

                // If no data found, try gaming interface parser
                if (tableData.length === 0) {
                    tableData = this.parseGamingInterface(extractedText, file.name);
                }

                const progress = ((globalIndex + 1) / this.selectedFiles.length) * 100;
                this.updateProgress(progress, `Completed ${file.name}`);

                if (tableData.length > 0) {
                    this.log(`✓ Extracted ${tableData.length} rows from ${file.name}`);
                    return tableData;
                } else {
                    this.log(`⚠ No tabular data found in ${file.name}`);
                    return [];
                }
            } catch (error) {
                this.log(`✗ Error processing ${file.name}: ${error.message}`);
                return [];
            }
        });

        const results = await Promise.all(promises);
        results.forEach(tableData => {
            if (tableData.length > 0) {
                this.extractedData.push(...tableData);
            }
        });
    }

    async extractTextFromImage(file) {
        return new Promise((resolve, reject) => {
            // Optimize image before OCR
            this.optimizeImageForOCR(file).then(optimizedFile => {
                const worker = Tesseract.createWorker();

                worker.load().then(() => {
                    return worker.loadLanguage('eng');
                }).then(() => {
                    return worker.initialize('eng', {
                        // Optimize for gaming interface and table detection
                        tessedit_pageseg_mode: Tesseract.PSM.SPARSE_TEXT,
                        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:\'\"<>?/~` ',
                        // Additional OCR parameters for better accuracy
                        tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
                        preserve_interword_spaces: '1',
                        user_defined_dpi: '300'
                    });
                }).then(() => {
                    return worker.recognize(optimizedFile);
                }).then(({ data: { text } }) => {
                    worker.terminate();
                    resolve(text);
                }).catch(error => {
                    worker.terminate();
                    reject(error);
                });
            }).catch(error => {
                reject(error);
            });
        });
    }

    async optimizeImageForOCR(file) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate optimal size (increase resolution for better OCR)
                const maxWidth = 2400;
                const maxHeight = 1600;
                let { width, height } = img;

                // Scale up small images for better OCR
                const minWidth = 1200;
                if (width < minWidth) {
                    const scale = minWidth / width;
                    width *= scale;
                    height *= scale;
                }

                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }

                if (height > maxHeight) {
                    width = (width * maxHeight) / height;
                    height = maxHeight;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw image with anti-aliasing disabled for sharper text
                ctx.imageSmoothingEnabled = false;
                ctx.drawImage(img, 0, 0, width, height);

                // Apply multiple enhancement techniques
                const imageData = ctx.getImageData(0, 0, width, height);
                this.enhanceImageForDarkBackground(imageData);
                ctx.putImageData(imageData, 0, 0);

                // Convert back to blob with high quality
                canvas.toBlob(resolve, 'image/png', 1.0);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    enhanceImageForDarkBackground(imageData) {
        const data = imageData.data;

        // First pass: Detect if image has dark background
        let darkPixels = 0;
        let totalPixels = 0;

        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            const brightness = (r + g + b) / 3;

            if (brightness < 100) darkPixels++;
            totalPixels++;
        }

        const isDarkBackground = (darkPixels / totalPixels) > 0.6;

        // Apply appropriate enhancement based on background
        if (isDarkBackground) {
            this.enhanceForDarkBackground(data);
        } else {
            this.enhanceContrast(data);
        }

        // Apply additional sharpening
        this.sharpenImage(imageData);
    }

    enhanceForDarkBackground(data) {
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Calculate brightness
            const brightness = (r + g + b) / 3;

            // If pixel is bright enough (likely text), make it white
            // If pixel is dark (likely background), make it black
            if (brightness > 80) {
                // Make text white
                data[i] = 255;     // Red
                data[i + 1] = 255; // Green
                data[i + 2] = 255; // Blue
            } else {
                // Make background black
                data[i] = 0;       // Red
                data[i + 1] = 0;   // Green
                data[i + 2] = 0;   // Blue
            }
        }
    }

    enhanceContrast(data) {
        const factor = 1.5; // Increased contrast factor
        const intercept = 128 * (1 - factor);

        for (let i = 0; i < data.length; i += 4) {
            // Apply contrast to RGB channels
            data[i] = Math.max(0, Math.min(255, data[i] * factor + intercept));     // Red
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor + intercept)); // Green
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor + intercept)); // Blue
        }
    }

    sharpenImage(imageData) {
        const data = imageData.data;
        const width = imageData.width;
        const height = imageData.height;
        const newData = new Uint8ClampedArray(data);

        // Sharpening kernel
        const kernel = [
            0, -1, 0,
            -1, 5, -1,
            0, -1, 0
        ];

        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                for (let c = 0; c < 3; c++) { // RGB channels only
                    let sum = 0;
                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                            const pixelIndex = ((y + ky) * width + (x + kx)) * 4 + c;
                            const kernelIndex = (ky + 1) * 3 + (kx + 1);
                            sum += data[pixelIndex] * kernel[kernelIndex];
                        }
                    }
                    const targetIndex = (y * width + x) * 4 + c;
                    newData[targetIndex] = Math.max(0, Math.min(255, sum));
                }
            }
        }

        // Copy sharpened data back
        for (let i = 0; i < data.length; i += 4) {
            data[i] = newData[i];       // Red
            data[i + 1] = newData[i + 1]; // Green
            data[i + 2] = newData[i + 2]; // Blue
            // Alpha remains unchanged
        }
    }

    parseTableData(text, fileName) {
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        const tableData = [];

        // Enhanced table detection with multiple strategies
        const detectedTables = this.detectTables(lines);

        detectedTables.forEach((table, tableIndex) => {
            table.rows.forEach((row, rowIndex) => {
                const rowData = {
                    source_file: fileName,
                    table_number: tableIndex + 1,
                    row_number: rowIndex + 1,
                    raw_text: row.original
                };

                // Add cells as columns
                row.cells.forEach((cell, cellIndex) => {
                    rowData[`column_${cellIndex + 1}`] = cell;
                });

                tableData.push(rowData);
            });
        });

        return tableData;
    }

    parseGamingInterface(text, fileName) {
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        const tableData = [];

        this.log(`Parsing gaming interface with ${lines.length} lines`);

        // Look for header patterns common in gaming interfaces
        let headerFound = false;
        let headerColumns = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip very short lines
            if (line.length < 5) continue;

            // Check if this looks like a header line
            if (!headerFound && this.isGamingHeader(line)) {
                headerColumns = this.extractHeaderColumns(line);
                headerFound = true;
                this.log(`Found gaming header: ${JSON.stringify(headerColumns)}`);
                continue;
            }

            // If we have a header, try to parse data rows
            if (headerFound) {
                const rowData = this.parseGamingDataRow(line, headerColumns, fileName, tableData.length + 1);
                if (rowData) {
                    tableData.push(rowData);
                    this.log(`Parsed gaming row: ${JSON.stringify(rowData)}`);
                }
            } else {
                // Try to parse without header
                const rowData = this.parseGamingDataRowNoHeader(line, fileName, tableData.length + 1);
                if (rowData) {
                    tableData.push(rowData);
                }
            }
        }

        return tableData;
    }

    isGamingHeader(line) {
        const upperLine = line.toUpperCase();
        const gamingKeywords = ['NAME', 'BATTING', 'BOWLING', 'RATING', 'HAND', 'PRICE', 'PLAYER', 'STATS'];
        let keywordCount = 0;

        gamingKeywords.forEach(keyword => {
            if (upperLine.includes(keyword)) keywordCount++;
        });

        return keywordCount >= 2;
    }

    extractHeaderColumns(line) {
        // Extract column headers from gaming interface
        const upperLine = line.toUpperCase();
        const columns = [];

        if (upperLine.includes('NAME')) columns.push('NAME');
        if (upperLine.includes('BATTING')) columns.push('BATTING_RATING');
        if (upperLine.includes('HAND')) columns.push('BATTING_HAND');
        if (upperLine.includes('BOWLING')) columns.push('BOWLING_RATING');
        if (upperLine.includes('PRICE')) columns.push('BASE_PRICE');

        return columns.length > 0 ? columns : ['NAME', 'VALUE1', 'VALUE2', 'VALUE3', 'VALUE4'];
    }

    parseGamingDataRow(line, headerColumns, fileName, rowNumber) {
        const cells = this.parseGamingInterfaceRow(line);

        if (cells.length < 2) return null;

        const rowData = {
            source_file: fileName,
            row_number: rowNumber,
            raw_text: line
        };

        // Map cells to header columns
        headerColumns.forEach((header, index) => {
            if (index < cells.length) {
                rowData[header] = cells[index];
            }
        });

        // Add any extra cells as additional columns
        for (let i = headerColumns.length; i < cells.length; i++) {
            rowData[`column_${i + 1}`] = cells[i];
        }

        return rowData;
    }

    parseGamingDataRowNoHeader(line, fileName, rowNumber) {
        const cells = this.parseGamingInterfaceRow(line);

        if (cells.length < 2) return null;

        const rowData = {
            source_file: fileName,
            row_number: rowNumber,
            raw_text: line
        };

        // Use generic column names
        cells.forEach((cell, index) => {
            rowData[`column_${index + 1}`] = cell;
        });

        return rowData;
    }

    detectTables(lines) {
        const tables = [];
        let currentTable = null;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip very short lines
            if (line.length < 5) {
                if (currentTable && currentTable.rows.length > 0) {
                    tables.push(currentTable);
                    currentTable = null;
                }
                continue;
            }

            const parsedRow = this.parseTableRow(line);

            if (parsedRow && parsedRow.cells.length >= 2) {
                if (!currentTable) {
                    currentTable = { rows: [] };
                }
                currentTable.rows.push(parsedRow);
            } else {
                // End current table if we hit a non-table line
                if (currentTable && currentTable.rows.length > 0) {
                    tables.push(currentTable);
                    currentTable = null;
                }
            }
        }

        // Add the last table if it exists
        if (currentTable && currentTable.rows.length > 0) {
            tables.push(currentTable);
        }

        return tables;
    }

    parseTableRow(line) {
        const original = line;
        let cells = [];

        // Clean the line first - remove extra whitespace and normalize
        const cleanLine = line.replace(/\s+/g, ' ').trim();

        // Strategy 1: Gaming interface pattern - detect fixed-width columns
        const gamingCells = this.parseGamingInterfaceRow(cleanLine);
        if (gamingCells.length >= 2) {
            cells = gamingCells;
        }

        // Strategy 2: Split by multiple spaces (most common)
        if (cells.length < 2) {
            const spaceSplit = cleanLine.split(/\s{2,}/).map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (spaceSplit.length >= 2) {
                cells = spaceSplit;
            }
        }

        // Strategy 3: Split by tabs
        if (cells.length < 2) {
            const tabSplit = line.split('\t').map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (tabSplit.length >= 2) {
                cells = tabSplit;
            }
        }

        // Strategy 4: Split by pipes or vertical bars
        if (cells.length < 2) {
            const pipeSplit = line.split(/[|│]/).map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (pipeSplit.length >= 2) {
                cells = pipeSplit;
            }
        }

        // Strategy 5: Split by commas (CSV-like)
        if (cells.length < 2) {
            const commaSplit = line.split(',').map(cell => cell.trim()).filter(cell => cell.length > 0);
            if (commaSplit.length >= 2) {
                cells = commaSplit;
            }
        }

        // Strategy 6: Detect aligned columns by position
        if (cells.length < 2) {
            cells = this.detectAlignedColumns(line);
        }

        // Strategy 7: Pattern-based extraction for gaming data
        if (cells.length < 2) {
            cells = this.extractGamingPatterns(cleanLine);
        }

        return cells.length >= 2 ? { original, cells } : null;
    }

    parseGamingInterfaceRow(line) {
        // Pattern for gaming interface: NAME + numbers/text in specific positions
        // Example: "A OMARSAI 55 RIGHT 60 100L"

        const cells = [];
        const words = line.split(/\s+/);

        if (words.length < 3) return [];

        // Try to identify patterns common in gaming interfaces
        let nameFound = false;
        let currentName = '';

        for (let i = 0; i < words.length; i++) {
            const word = words[i];

            // If we haven't found a name yet and this looks like a name
            if (!nameFound && this.isLikelyName(word)) {
                currentName = word;
                // Check if next word is also part of name
                if (i + 1 < words.length && this.isLikelyName(words[i + 1])) {
                    currentName += ' ' + words[i + 1];
                    i++; // Skip next word as it's part of name
                }
                cells.push(currentName);
                nameFound = true;
            }
            // If this looks like a number or specific value
            else if (nameFound && (this.isNumber(word) || this.isGameValue(word))) {
                cells.push(word);
            }
        }

        return cells;
    }

    isLikelyName(word) {
        // Check if word looks like a name (contains letters, not just numbers)
        return /^[A-Za-z]/.test(word) && word.length > 1;
    }

    isNumber(word) {
        return /^\d+$/.test(word);
    }

    isGameValue(word) {
        // Common gaming values: LEFT, RIGHT, numbers with L suffix, etc.
        return /^(LEFT|RIGHT|[0-9]+L?)$/i.test(word);
    }

    extractGamingPatterns(line) {
        const cells = [];

        // Pattern 1: Extract names (usually at the beginning)
        const nameMatch = line.match(/^([A-Z\s]+?)(?=\s+\d)/);
        if (nameMatch) {
            cells.push(nameMatch[1].trim());
        }

        // Pattern 2: Extract all numbers
        const numbers = line.match(/\d+/g);
        if (numbers) {
            cells.push(...numbers);
        }

        // Pattern 3: Extract LEFT/RIGHT
        const direction = line.match(/(LEFT|RIGHT)/i);
        if (direction) {
            cells.push(direction[1]);
        }

        // Pattern 4: Extract values with L suffix (like 100L, 20L)
        const lValues = line.match(/\d+L/g);
        if (lValues) {
            cells.push(...lValues);
        }

        return cells;
    }

    detectAlignedColumns(line) {
        // Simple column detection based on character positions
        const words = [];
        let currentWord = '';
        let wordStart = 0;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === ' ') {
                if (currentWord.trim().length > 0) {
                    words.push({
                        text: currentWord.trim(),
                        start: wordStart,
                        end: i
                    });
                    currentWord = '';
                }
            } else {
                if (currentWord === '') {
                    wordStart = i;
                }
                currentWord += char;
            }
        }

        // Add the last word
        if (currentWord.trim().length > 0) {
            words.push({
                text: currentWord.trim(),
                start: wordStart,
                end: line.length
            });
        }

        // Group words into columns based on spacing
        const columns = [];
        let currentColumn = [];

        for (let i = 0; i < words.length; i++) {
            const word = words[i];
            const nextWord = words[i + 1];

            currentColumn.push(word.text);

            // If there's a significant gap to the next word, end the column
            if (nextWord && (nextWord.start - word.end) > 3) {
                columns.push(currentColumn.join(' '));
                currentColumn = [];
            }
        }

        // Add the last column
        if (currentColumn.length > 0) {
            columns.push(currentColumn.join(' '));
        }

        return columns;
    }

    updateProgress(percentage, message) {
        const progressBar = document.getElementById('overallProgressBar');
        const progressText = document.getElementById('overallProgress');
        
        progressBar.style.width = `${percentage}%`;
        progressText.textContent = `${Math.round(percentage)}%`;
        
        if (message) {
            this.log(message);
        }
    }

    log(message) {
        const logElement = document.getElementById('processingLog');
        const timestamp = new Date().toLocaleTimeString();
        logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logElement.scrollTop = logElement.scrollHeight;
    }

    showResults() {
        const resultsSection = document.getElementById('resultsSection');
        const processedCount = document.getElementById('processedCount');
        const totalRows = document.getElementById('totalRows');
        
        resultsSection.classList.remove('hidden');
        processedCount.textContent = this.selectedFiles.length;
        totalRows.textContent = this.extractedData.length;
    }

    togglePreview() {
        const dataPreview = document.getElementById('dataPreview');
        const previewBtn = document.getElementById('previewBtn');
        
        if (dataPreview.classList.contains('hidden')) {
            this.generatePreview();
            dataPreview.classList.remove('hidden');
            previewBtn.textContent = 'Hide Preview';
        } else {
            dataPreview.classList.add('hidden');
            previewBtn.textContent = 'Preview Data';
        }
    }

    generatePreview() {
        if (this.extractedData.length === 0) return;
        
        const tableHead = document.getElementById('previewTableHead');
        const tableBody = document.getElementById('previewTableBody');
        
        // Get all unique column names
        const allColumns = new Set();
        this.extractedData.forEach(row => {
            Object.keys(row).forEach(key => allColumns.add(key));
        });
        
        const columns = Array.from(allColumns);
        
        // Create header
        tableHead.innerHTML = `<tr>${columns.map(col => `<th class="px-4 py-2 border">${col}</th>`).join('')}</tr>`;
        
        // Create body (show first 10 rows)
        const previewData = this.extractedData.slice(0, 10);
        tableBody.innerHTML = previewData.map(row => 
            `<tr>${columns.map(col => `<td class="px-4 py-2 border">${row[col] || ''}</td>`).join('')}</tr>`
        ).join('');
    }

    downloadExcel() {
        if (this.extractedData.length === 0) {
            alert('No data to download');
            return;
        }

        // Create workbook
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(this.extractedData);
        
        XLSX.utils.book_append_sheet(wb, ws, 'Extracted Data');
        
        // Generate filename with timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
        const filename = `extracted_data_${timestamp}.xlsx`;
        
        // Download file
        XLSX.writeFile(wb, filename);
        
        this.log(`Excel file downloaded: ${filename}`);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new ImageToExcelConverter();
});
